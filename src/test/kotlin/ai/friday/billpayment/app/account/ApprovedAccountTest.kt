package ai.friday.billpayment.app.account

import ai.friday.billpayment.accountRegisterCompleted
import ai.friday.billpayment.accountRegisterData
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.billpayment.app.documentscan.DocumentScanService
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.ECMProvider
import ai.friday.billpayment.app.integrations.ExternalAccountRegister
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.RegisterPixKeyCommand
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.balance
import ai.friday.billpayment.integration.AccountFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.getOrElse
import arrow.core.right
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test

class ApprovedAccountTest {

    private val accountRegisterRepository = mockk<AccountRegisterRepository>()
    private val accountService = mockk<AccountService>() {
        every {
            addGroupsToAccount(any<AccountId>(), any())
        } returns mockk()
    }
    private val accountRegisterService = mockk<AccountRegisterService>()
    private val externalAccountRegister = mockk<ExternalAccountRegister>()
    private val pixKeyManagement = mockk<PixKeyManagement>() {
        every {
            registerKey(any(), any(), any(), any(), any())
        } returns PixKey(value = "***********", type = PixKeyType.CPF)
    }
    private val ddaService = mockk<DDAService>()
    private val modattaProvider = mockk<ModattaProvider>(relaxed = true)
    private val walletService: WalletService = mockk(relaxed = true)
    private val walletLimitsService: WalletLimitsService = mockk(relaxed = true)
    private val crmService = mockk<CrmService>(relaxed = true)
    private val documentScanServiceMock: DocumentScanService = mockk {
        every { getResult(any()) } returns null.right()
        every { getImage(any()) } returns null.right()
    }
    private val ecmProvider = mockk<ECMProvider>(relaxed = true)
    private val messagePublisherMock = mockk<MessagePublisher>(relaxed = true)
    private val walletBillCategoryService = mockk<PFMWalletCategoryService>(relaxed = true)

    private val registerService: RegisterService =
        RegisterService(
            tokenService = mockk(),
            accountRegisterRepository = accountRegisterRepository,
            accountService = accountService,
            accountRegisterService = accountRegisterService,
            documentOCRParser = mockk(),
            userFilesConfiguration = mockk(),
            userAddressConfiguration = mockk(relaxed = true),
            agreementFilesService = mockk(),
            notificationSenderService = mockk(relaxed = true),
            ddaService = ddaService,
            notificationAdapter = mockk(relaxed = true),
            userPoolAdapter = mockk(relaxed = true),
            kycService = mockk(),
            externalAccountRegister = externalAccountRegister,
            ecmProvider = ecmProvider,
            walletService = walletService,
            walletLimitsService = walletLimitsService,
            crmService = crmService,
            registerInstrumentationService = mockk(relaxed = true),
            pdfFileParser = mockk(),
            systemActivityService = mockk(relaxUnitFun = true),
            pendingInternalApproveConfiguration = mockk(),
            pendingInternalReviewConfiguration = mockk(),
            pendingActivationConfiguration = NewAccountEmailConfiguration(
                name = "",
                recipients = "",
                sensitiveRecipients = "",
                subject = "",
                message = "",
            ),
            accountStatusLockProvider = mockk() {
                every { acquireLock(any()) } returns mockk(relaxed = true)
            },
            bigDataService = mockk(),
            userJourneyService = mockk(relaxUnitFun = true),
            closeAccountService = mockk(relaxed = true) {},
            livenessService = mockk(),
            modattaProvider = modattaProvider,
            eventPublisher = mockk(relaxed = true),
            fraudList = mockk(relaxed = true),
            pendingUpgradeInternalApproveConfiguration = mockk(relaxed = true),
            messagePublisher = messagePublisherMock,
            adService = mockk(relaxed = true),
            chatBotNotificationService = mockk(relaxed = true),
            walletBillCategoryService = walletBillCategoryService,
            documentScanService = documentScanServiceMock,
            activityService = mockk(relaxed = true),
            loginRepository = mockk(relaxed = true),
        ).apply {
            registerPixKeyQueueName = ""
            maxDocumentSize = 10
            from = ""
            pixKeyEmailDomain = "friday.ai"
        }

    @Test
    fun `nao deve chamar o registernaturalperson se a pessoa ja possui bank account ativo`() {
        val currentRegister = accountRegisterCompleted

        every {
            accountRegisterRepository.findByDocument("***********")
        } returns currentRegister

        every {
            accountService.findAccountByIdOrNull(any())
        } returns null

        every {
            accountService.findAccountByDocumentOrNull("***********")
        } returns null

        every {
            accountService.findPartialAccountById(currentRegister.accountId)
        } returns PartialAccount(
            id = currentRegister.accountId,
            name = currentRegister.documentInfo!!.name,
            emailAddress = currentRegister.emailAddress,
            status = AccountStatus.UNDER_EXTERNAL_REVIEW,
            statusUpdated = getZonedDateTime(),
            role = Role.GUEST,
            groups = emptyList(),
            registrationType = RegistrationType.FULL,
            subscriptionType = SubscriptionType.PIX,
        )

        every {
            accountService.findPhysicalBankAccountByAccountId(currentRegister.accountId)
        } returns listOf(balance)

        every {
            accountService.updatePartialAccountStatus(currentRegister.accountId, AccountStatus.APPROVED)
        } just Runs

        every {
            ddaService.register(any(), any())
        } returns DDAStatus.REQUESTED

        val status =
            registerService.updateAccountStatus(document = "***********", status = ExternalRegisterStatus.APPROVED)

        verify(exactly = 0) {
            externalAccountRegister.registerNaturalPerson(any())
        }

        verify(exactly = 1) {
            accountService.updatePartialAccountStatus(currentRegister.accountId, AccountStatus.APPROVED)
            messagePublisherMock.sendMessage(
                "",
                RegisterPixKeyCommand(
                    accountNo = AccountNumber(
                        (balance.method as InternalBankAccount).accountNo.toBigInteger(),
                        (balance.method as InternalBankAccount).accountDv,
                    ),
                    key = PixKey("<EMAIL>", PixKeyType.EMAIL),
                    document = "***********",
                    name = currentRegister.documentInfo!!.name,
                    walletId = WalletId(currentRegister.accountId.value),
                ),
            )

            ddaService.register(accountRegisterData.accountId, "***********")
        }

        status.isRight() shouldBe true

        status.map {
            it shouldBe SetupAccountResult.AccountApproved
        }
    }

    @Test
    fun `deve ativar a conta sem DDA e chamar o callback de atualização de estado do simple signup quando for usuario modatta`() {
        val currentRegister = accountRegisterCompleted.copy(
            externalId = ExternalId("modatta", AccountProviderName.MODATTA),
        )

        every {
            accountRegisterRepository.findByDocument("***********")
        } returns currentRegister

        every {
            accountService.findAccountByDocumentOrNull("***********")
        } returns null

        val partialAccount = PartialAccount(
            id = currentRegister.accountId,
            name = currentRegister.documentInfo!!.name,
            emailAddress = currentRegister.emailAddress,
            status = AccountStatus.UNDER_EXTERNAL_REVIEW,
            statusUpdated = getZonedDateTime(),
            role = Role.GUEST,
            groups = emptyList(),
            registrationType = RegistrationType.FULL,
            subscriptionType = SubscriptionType.PIX,
        )
        every {
            accountService.findPartialAccountById(currentRegister.accountId)
        } returns partialAccount andThen partialAccount.copy(status = AccountStatus.APPROVED)

        every {
            accountService.findPhysicalBankAccountByAccountId(currentRegister.accountId)
        } throws PaymentMethodNotFound(currentRegister.accountId) andThen listOf(balance)

        every {
            accountService.create(any())
        } answers {
            firstArg<Account>()
        }

        val account = Account(
            name = currentRegister.documentInfo!!.name,
            emailAddress = currentRegister.emailAddress,
            status = AccountStatus.UNDER_EXTERNAL_REVIEW,
            accountId = currentRegister.accountId,
            document = "",
            documentType = "",
            mobilePhone = "",
            created = getZonedDateTime(),
            updated = getZonedDateTime(),
            activated = null,
            configuration = LegacyAccountConfiguration(
                accountId = null,
                creditCardConfiguration = CreditCardConfiguration(
                    quota = 0,
                ),
                defaultWalletId = null,
                receiveDDANotification = false,
                receiveNotification = false,
                accessToken = null,
                refreshToken = null,
                externalId = null,
                groups = listOf(),
                notificationGateway = NotificationGateways.WHATSAPP,
            ),
            firstLoginAsOwner = null,
            channel = null,
            imageUrlSmall = null,
            imageUrlLarge = null,
            subscriptionType = SubscriptionType.PIX,
        )

        every {
            accountService.findAccountById(currentRegister.accountId)
        } returns account

        every {
            accountService.findAccountByIdOrNull(currentRegister.accountId)
        } returns account

        every {
            accountService.addGroupsToAccount(any<AccountId>(), any())
        } returns account

        every {
            accountService.createAccountPaymentMethod(any(), any(), any(), any())
        } returns balance

        every {
            accountService.save(any())
        } just runs

        every {
            externalAccountRegister.simpleRegisterNaturalPerson(any())
        } returns RegisterNaturalPersonResponse(true, 10000, "1")

        every {
            accountService.updatePartialAccountStatus(currentRegister.accountId, AccountStatus.APPROVED)
        } just Runs

        every {
            pixKeyManagement.registerKey(any(), any(), any(), any(), any())
        } returns PixKey(value = "<EMAIL>", type = PixKeyType.EMAIL)

        every {
            accountRegisterRepository.findByAccountId(currentRegister.accountId)
        } returns currentRegister

        every {
            accountService.deletePartialAccount(currentRegister.accountId)
        } just runs

        every {
            crmService.contactExists(any())
        } returns true

        every {
            accountService.enableCreditCardUsage(
                accountId = any(),
                quota = 2_000_00,
                sendNotification = false,
            )
        } returns account.copy(status = AccountStatus.ACTIVE).right()

        val status = registerService.updateAccountStatus(
            document = "***********",
            status = ExternalRegisterStatus.APPROVED,
        )

        status.isRight() shouldBe true
        status.getOrElse { throw it } shouldBe SetupAccountResult.AccountApproved
        verify {
            externalAccountRegister.simpleRegisterNaturalPerson(any())
            ddaService.register(any(), any()) wasNot Called
            accountService.updatePartialAccountStatus(currentRegister.accountId, AccountStatus.APPROVED)
            messagePublisherMock.sendMessage(
                "",
                RegisterPixKeyCommand(
                    accountNo = AccountNumber(
                        (balance.method as InternalBankAccount).accountNo.toBigInteger(),
                        (balance.method as InternalBankAccount).accountDv,
                    ),
                    key = PixKey("<EMAIL>", PixKeyType.EMAIL),
                    document = "***********",
                    name = currentRegister.documentInfo!!.name,
                    walletId = WalletId(account.accountId.value),
                ),
            )

            modattaProvider.updateStatus(
                accountId = currentRegister.accountId,
                status = AccountStatus.ACTIVE,
                externalId = ExternalId("modatta", AccountProviderName.MODATTA),
                approvedBy = SimpleSignUpApprover.EXTERNAL_ACCOUNT_REGISTER,
            )
            accountService.deletePartialAccount(currentRegister.accountId)
            walletLimitsService.updateLimit(
                accountId = any(),
                walletId = any(),
                type = DailyPaymentLimitType.MONTHLY,
                amount = 10_000_00,
                source = ActionSource.System,
            )

            walletLimitsService.updateLimit(
                accountId = any(),
                walletId = any(),
                type = DailyPaymentLimitType.DAILY,
                amount = 500_00,
                source = ActionSource.System,
            )

            walletBillCategoryService.createDefaultWalletCategories(any())
        }
    }

    @Test
    fun `deve alterar o tipo da conta para FULL_ACCOUNT quando o callback de upgrade for SUCESSO`() {
        val currentRegister = accountRegisterCompleted
        val currentAccount = AccountFixture.createAccount(
            accountID = currentRegister.accountId.value,
        ).copy(
            type = UserAccountType.BASIC_ACCOUNT,
            upgradeStatus = UpgradeStatus.UNDER_EXTERNAL_REVIEW,
        )

        every { accountService.save(any()) } just runs
        every { walletLimitsService.setDefaultFullAccountLimits(any()) } just runs
        every {
            accountRegisterRepository.findByDocument("***********")
        } returns currentRegister

        every {
            accountService.findAccountByIdOrNull(any())
        } returns currentAccount

        val status =
            registerService.updateAccountStatus(document = "***********", status = ExternalRegisterStatus.APPROVED)

        status.isRight().shouldBeTrue()
        status.getOrElse { throw it } shouldBe SetupAccountResult.AccountApproved

        verify {
            ecmProvider.notifyUpgradeStatus(
                NotifyUpgradeStatusRequest(
                    currentAccount.document,
                    ExternalRegisterStatus.APPROVED,
                ),
            )
            accountService.save(
                currentAccount.copy(
                    upgradeStatus = UpgradeStatus.COMPLETED,
                    type = UserAccountType.FULL_ACCOUNT,
                ),
            )
        }
    }

    @Test
    fun `não deve alterar o tipo da conta quando o callback de upgrade for ERRO`() {
        val currentRegister = accountRegisterCompleted
        val currentAccount = AccountFixture.createAccount(
            accountID = currentRegister.accountId.value,
        ).copy(
            type = UserAccountType.BASIC_ACCOUNT,
            upgradeStatus = UpgradeStatus.UNDER_EXTERNAL_REVIEW,
        )

        every { accountService.save(any()) } just runs
        every { walletLimitsService.setDefaultFullAccountLimits(any()) } just runs
        every {
            accountRegisterRepository.findByDocument("***********")
        } returns currentRegister

        every {
            accountService.findAccountByIdOrNull(any())
        } returns currentAccount

        val status =
            registerService.updateAccountStatus(document = "***********", status = ExternalRegisterStatus.REJECTED)

        status.isRight().shouldBeTrue()
        status.getOrElse { throw it } shouldBe SetupAccountResult.AccountRejected

        verify(exactly = 0) {
            walletLimitsService.setDefaultFullAccountLimits(any())
        }
        verify {
            accountService.save(currentAccount.copy(upgradeStatus = UpgradeStatus.EXTERNAL_DENIED))
            ecmProvider.notifyUpgradeStatus(
                NotifyUpgradeStatusRequest(
                    currentAccount.document,
                    ExternalRegisterStatus.REJECTED,
                ),
            )
        }
    }
}