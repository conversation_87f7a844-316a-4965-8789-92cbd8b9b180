package ai.friday.billpayment.adapters.auth

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.feature.RequiresCognito
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.login.LoginService
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.morning.log.andAppend
import com.nimbusds.jwt.JWT
import com.nimbusds.jwt.JWTClaimsSet
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.EachBean
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Replaces
import io.micronaut.security.authentication.Authentication
import io.micronaut.security.token.jwt.validator.DefaultJwtAuthenticationFactory
import io.micronaut.security.token.jwt.validator.JwtAuthenticationFactory
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.util.*
import net.logstash.logback.marker.Markers.append
import org.slf4j.Logger
import org.slf4j.LoggerFactory

const val FROM_CLAIM_USERNAME = "cognito:username"
const val FROM_CLAIM_ACCOUNT_ID = "custom:accountId"
const val FROM_CLAIM_ROLES = "cognito:groups"
const val FROM_CLAIM_EMAIL = "email"
const val FROM_CLAIM_NAME = "name"

const val TO_CLAIM_ACCOUNT_ID = "id"
const val TO_CLAIM_SUBJECT = "sub"
const val TO_CLAIM_ISSUER = "iss"
const val TO_CLAIM_EMAIL = "email"
const val TO_CLAIM_NAME = "nickname"
const val TO_CLAIM_PROVIDER_NAME = "providerName"
const val TO_CLAIM_PROVIDER_USERNAME = "providerUsername"
const val TO_CLAIM_ACTUAL_ROLE = "actualRole"
const val TO_CLAIM_MSISDN_MIGRATED = "msisdn:migrated"

@Singleton
@Replaces(bean = DefaultJwtAuthenticationFactory::class)
@RequiresCognito
class CustomJwtAuthenticationFactory(
    cognitoJwtAuthenticationFactory: CognitoJwtAuthenticationFactory,
    msisdnJwtAuthenticationFactory: MsisdnJwtAuthenticationFactory,
) : JwtAuthenticationFactory {
    private val authenticationFactories =
        listOf(
            cognitoJwtAuthenticationFactory,
            msisdnJwtAuthenticationFactory,
        )

    override fun createAuthentication(token: JWT?): Optional<Authentication> {
        return token?.let { jwt ->
            authenticationFactories.asSequence().map { it.createAuthentication(jwt) }.firstOrNull { it.isPresent }
                ?: Optional.empty()
        } ?: Optional.empty()
    }
}

@Singleton
@RequiresCognito
class CognitoJwtAuthenticationFactory(
    private val loginService: LoginService,
    private val userPoolAdapter: UserPoolAdapter,
) : AbstractJwtAuthenticationFactory() {
    override fun createAuthentication(token: JWT): Optional<Authentication> {
        val logName = "CognitoJwtAuthenticationFactory"
        val markers = append("fromClaimsSet", token.jwtClaimsSet.claims)

        if (!token.jwtClaimsSet.claims.containsKey(FROM_CLAIM_USERNAME)) {
            logger.warn(markers, logName)
            return Optional.empty()
        }

        try {
            val authentication = if (token.hasAccountId()) {
                token.toAuthentication()
            } else {
                val providerUser = token.toProviderUser()
                markers.andAppend("providerUser", providerUser)

                val loginResult = loginService.resolveLogin(providerUser)
                markers.andAppend("loginResult", loginResult)

                val userRoles = listOf(loginResult.login.role.name)

                userPoolAdapter.configureUser(
                    token.username(),
                    loginResult.login.accountId,
                    userRoles,
                )

                buildAuthentication(
                    accountId = loginResult.login.accountId.value,
                    name = token.name(),
                    email = token.email(),
                    roles = userRoles,
                    providerName = ProviderName.COGNITO,
                    providerUsername = token.username(),
                )
            }
            markers.andAppend("authentication", authentication)

            logger.info(markers, logName)
            return Optional.of(authentication)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return Optional.empty()
        }
    }

    private fun JWT.hasAccountId() = jwtClaimsSet.claims.containsKey(FROM_CLAIM_ACCOUNT_ID)

    private fun JWT.toAuthentication() = with(jwtClaimsSet) {
        buildAuthentication(
            accountId = getStringClaim(FROM_CLAIM_ACCOUNT_ID),
            name = name(),
            email = email(),
            roles = getClaim(FROM_CLAIM_ROLES) as Collection<String>,
            providerName = ProviderName.COGNITO,
            providerUsername = username(),
        )
    }

    private fun JWT.username() = jwtClaimsSet.getStringClaim(FROM_CLAIM_USERNAME)
    private fun JWT.email() = jwtClaimsSet.getStringClaim(FROM_CLAIM_EMAIL)
    private fun JWT.name() = jwtClaimsSet.getStringClaim(FROM_CLAIM_NAME).orEmpty()

    private fun JWT.toProviderUser() = ProviderUser(
        id = username(),
        providerName = ProviderName.COGNITO,
        emailAddress = EmailAddress(email()),
        username = name(),
    )
}

@FridayMePoupe
class MsisdnJwtAuthenticationFactory(
    @Named("msisdn") private val jwtValidator: JwtValidator,
) : AbstractJwtAuthenticationFactory() {
    override fun createAuthentication(token: JWT): Optional<Authentication> {
        val markers = append("fromClaimsSet", token.jwtClaimsSet.claims)
        if (!jwtValidator.validate(token.jwtClaimsSet)) {
            return Optional.empty()
        }

        try {
            val email = token.jwtClaimsSet.getStringClaim(FROM_CLAIM_EMAIL)
            val actualRole = token.jwtClaimsSet.getStringClaim(TO_CLAIM_ACTUAL_ROLE)
            val migrated = token.jwtClaimsSet.getBooleanClaim(TO_CLAIM_MSISDN_MIGRATED)

            val providerId = token.jwtClaimsSet.subject

            return Optional.of(
                buildAuthentication(
                    accountId = providerId,
                    name = providerId, // FIXME - melhor usar o numero de telefone?
                    email = email,
                    actualRole = actualRole,
                    migrated = migrated,
                    roles = listOf(Role.GUEST.name, Role.GUEST_OTP.name),
                    providerName = ProviderName.MSISDN,
                    providerUsername = providerId, // FIXME - melhor usar o numero de telefone?
                ),
            )
        } catch (e: Exception) {
            logger.error(markers, this.javaClass.simpleName, e)
        }
        return Optional.empty()
    }
}

abstract class AbstractJwtAuthenticationFactory {
    abstract fun createAuthentication(token: JWT): Optional<Authentication>

    fun buildAuthentication(
        accountId: String,
        name: String,
        email: String,
        roles: Collection<String>,
        actualRole: String? = null,
        migrated: Boolean? = null,
        providerName: ProviderName,
        providerUsername: String,
    ): Authentication {
        val attributes =
            mapOf(
                TO_CLAIM_SUBJECT to accountId,
                TO_CLAIM_EMAIL to email,
                TO_CLAIM_ACCOUNT_ID to accountId,
                TO_CLAIM_NAME to name,
                TO_CLAIM_PROVIDER_NAME to providerName,
                TO_CLAIM_PROVIDER_USERNAME to providerUsername,
                TO_CLAIM_MSISDN_MIGRATED to migrated,
                TO_CLAIM_ACTUAL_ROLE to actualRole,
            )

        val allowedRoles = Role.entries.map { it.name }
        val filteredRoles = roles.filter { allowedRoles.contains(it) }

        return Authentication.build(
            accountId,
            filteredRoles,
            attributes,
        )
    }

    companion object {
        val logger: Logger = LoggerFactory.getLogger(AbstractJwtAuthenticationFactory::class.java)
    }
}

@EachProperty("jwt-validation.providers")
class JwtValidationConfiguration
@ConfigurationInject
constructor(
    val audience: List<String>,
    val issuer: String,
)

@EachBean(JwtValidationConfiguration::class)
class JwtValidator(val configuration: JwtValidationConfiguration) {
    fun validate(jwtClaimsSet: JWTClaimsSet) =
        jwtClaimsSet.claims[TO_CLAIM_ISSUER] == configuration.issuer && validateAudience(jwtClaimsSet)

    private fun validateAudience(claimsSet: JWTClaimsSet): Boolean {
        if (configuration.audience.isEmpty()) {
            return true
        }
        val audience: List<String> = claimsSet.audience
        if (audience.isEmpty()) {
            return false
        }
        if (!configuration.audience.containsAll(audience)) {
            return false
        }

        return true
    }
}

private fun getSanitizedEmailAddress(token: JWT) = EmailAddress(token.jwtClaimsSet.getStringClaim("email").lowercase())