package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.CNPJ_SIZE
import ai.friday.billpayment.app.CPF_SIZE
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountIsNotUnderExternalReview
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.ExternalRegisterStatus
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.account.RegisterNaturalPersonException
import ai.friday.billpayment.app.account.RegisterService
import ai.friday.billpayment.app.account.Role.Code.ARBI_CALLBACK
import ai.friday.billpayment.app.account.SetupAccountResult
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.banking.ExternalBankAccount
import ai.friday.billpayment.app.banking.ExternalBankAccountService
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.banking.OmnibusBankAccountConfiguration
import ai.friday.billpayment.app.banking.SynchronizeBankAccountTO
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.chatbot.CashInReceivedTO
import ai.friday.billpayment.app.integrations.AlreadyLockedException
import ai.friday.billpayment.app.integrations.EndToEnd
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.SweepingAccountServiceInterface
import ai.friday.billpayment.app.journey.UserJourneyEvent
import ai.friday.billpayment.app.journey.UserJourneyService
import ai.friday.billpayment.app.onepixpay.OnePixPayId
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.onepixpay.QrCodeTransactionId
import ai.friday.billpayment.app.onepixpay.UUIDTransactionId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.toEpochMillis
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import com.fasterxml.jackson.core.JsonProcessingException
import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.MutableHttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.ClientFilter
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Filter
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.RequestFilter
import io.micronaut.http.annotation.ResponseFilter
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.filter.HttpServerFilter
import io.micronaut.http.filter.ServerFilterChain
import io.micronaut.security.annotation.Secured
import io.reactivex.Flowable
import io.reactivex.schedulers.Schedulers
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import kotlin.jvm.optionals.getOrNull
import kotlin.math.roundToLong
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.reactivestreams.Publisher
import org.slf4j.LoggerFactory

private const val ARBI_BANK_NO = 213L

private const val BASIC_ACCOUNT_CASH_IN_LIMIT = 10_000_00

@FridayMePoupe
@ClientFilter(
    patterns = [
        "/pagamentos/**",
        "/cadastropf/**",
        "/dominios/**",
        "/contacorrente/**",
        "/checking-account/api/**",
        "/pix/**",
        "/cadastromanutencao/**",
    ],
)
class ArbiClientFilter {
    private val logger = LoggerFactory.getLogger(ArbiClientFilter::class.java)

    @RequestFilter
    fun filterRequest(request: MutableHttpRequest<*>) {
        request.setAttribute("startTime", getZonedDateTime().format(DateTimeFormatter.ISO_ZONED_DATE_TIME))
    }

    @ResponseFilter
    fun filterResponse(request: MutableHttpRequest<*>, response: HttpResponse<*>?, failure: Throwable?) {
        val startTime: ZonedDateTime? = request.getAttribute("startTime")?.orElse(null)?.let {
            ZonedDateTime.parse(it as String)
        }

        val clientReponse = response ?: failure?.takeIf { it is HttpClientResponseException }?.let { (it as HttpClientResponseException).response }

        val markers = append("body", request.body)
            .andAppend("path", request.uri.path)
            .andAppend("method", request.method.name)
            .andAppend("status", clientReponse?.status?.code)
            .andAppend("responseBody", clientReponse?.body())

        if (startTime != null) {
            markers.andAppend("duration", getZonedDateTime().toEpochMillis() - startTime.toEpochMillis())
        }

        logger.info(markers, "ArbiClientFilter", failure)
    }
}

@FridayMePoupe
@Filter(value = ["/arbi/**"])
class ArbiCallbackControllerLoggingFilter : HttpServerFilter {

    private val logger = LoggerFactory.getLogger(ArbiCallbackControllerLoggingFilter::class.java)

    override fun doFilter(request: HttpRequest<*>, chain: ServerFilterChain): Publisher<MutableHttpResponse<*>> {
        return Flowable.fromCallable { true }
            .subscribeOn(Schedulers.io())
            .switchMap { chain.proceed(request) }
            .doOnNext { res ->
                val marker = append("method", request.methodName)
                    .andAppend("uri", request.uri)
                    .andAppend("responseStatus", res.status().code)

                try {
                    marker.andAppend("responseBody", res.getBody(Map::class.java).getOrNull() ?: "N/A")
                        .andAppend("responseBodyString", res.getBody(String::class.java).getOrNull() ?: "N/A")
                        .andAppend("payload", request.getBody(Map::class.java).getOrNull() ?: "N/A")
                        .andAppend("payloadString", request.getBody(String::class.java).getOrNull() ?: "N/A")

                    logger.info(marker, "ArbiCallbackControllerLoggingFilter")
                } catch (ex: Exception) {
                    logger.error(marker, "ArbiCallbackControllerLoggingFilter", ex)
                }
            }
    }
}

@Secured(ARBI_CALLBACK)
@Controller("/arbi")
@FridayMePoupe
open class ArbiDepositCallbackController(
    private val internalBankService: InternalBankService,
    private val externalBankAccountService: ExternalBankAccountService,
    private val accountService: AccountService,
    private val omnibusBankAccountConfiguration: OmnibusBankAccountConfiguration,
    private val userJourneyService: UserJourneyService,
    private val messagePublisher: MessagePublisher,
    private val onePixPayService: OnePixPayService,
    private val sweepingAccountService: SweepingAccountServiceInterface,
    private val billEventDBRepository: BillEventDBRepository,
    private val walletService: WalletService,
    @Property(name = "sqs.queues.pixDepositNotification") private val pixDepositNotificationQueue: String,
    @Property(name = "sqs.queues.cashInReceived") private val cashInReceivedQueue: String,
) {



    @Post("/deposit-pix")
    fun arbiDepositPixCallback(@Body payload: String): HttpResponse<*> {
        val markers = Markers.empty()
        val logName = "arbiPixDepositCallback"

        if (payload.contains("SingleEntryCode")) { // FIXME novo cash in cash out do arbi
            LOG.info(markers.andAppend("payload", payload), logName)
            return HttpResponse.noContent<Unit>()
        }

        try {
            val payloadMessage = parseObjectFrom<ArbiPixMovementCallbackRequest>(payload).mensagem
            markers.andAppend("payloadMessage", payloadMessage)

            if (payloadMessage.naturezaMovimento == ArbiPixMovementNature.C) {
                when (payloadMessage.tipo) {
                    ArbiPixMovementType.CREDITO_EXTERNO_CANCELADO,
                    ArbiPixMovementType.CREDITO_INTERNO_CANCELADO,
                    ArbiPixMovementType.CREDITO_DEVOLUCAO_CANCELADO,
                    -> handleCreditoCancelado(markers)

                    ArbiPixMovementType.CREDITO_EXTERNO_NAO_FINALIZADO,
                    ArbiPixMovementType.CREDITO_INTERNO_NAO_FINALIZADO,
                    ArbiPixMovementType.CREDITO_DEVOLUCAO_NAO_FINALIZADO,
                    -> handleCreditoNaoFinalizado(payloadMessage, markers)

                    ArbiPixMovementType.CREDITO_INTERNO,
                    ArbiPixMovementType.CREDITO_EXTERNO,
                    ArbiPixMovementType.CREDITO_DEVOLUCAO,
                    -> handleDeposit(payloadMessage, markers).onLeft {
                        LOG.warn(markers, logName)
                        return it
                    }

                    else -> {
                        LOG.error(markers.andAppend("ACTION", "VERIFY"), logName)
                        return HttpResponse.badRequest("Tipo de mensagem inesperada para natureza de movimento C: ${payloadMessage.tipo}")
                    }
                }
            }

            LOG.info(markers, logName)
            return HttpResponse.noContent<Unit>()
        } catch (exception: JsonProcessingException) {
            markers.andAppend("payload", payload)

            return if (!payload.contains("MOVIMENTO")) {
                LOG.warn(markers, logName, exception)
                HttpResponse.noContent<Unit>()
            } else {
                markers.andAppend("ACTION", "VERIFY")
                    .andAppend("context", "Payload da requisição inválido.")
                    .andAppend("errorMessage", exception.message)
                LOG.error(markers, logName, exception)
                HttpResponse.badRequest("Requisição inválida. Por favor, verifique o body enviado e tente novamente. ${exception.message}")
            }
        } catch (e: Exception) {
            LOG.error(markers, logName, e)
            return HttpResponse.serverError<Unit>()
        }
    }

    private fun handleCreditoCancelado(markers: LogstashMarker) {
        markers.andAppend(
            "context",
            "Dinheiro não saiu da conta de origem porque o Arbi provavelmente desativou a conta bancária do usuário",
        )
    }

    private fun handleCreditoNaoFinalizado(message: ArbiPixMovementNotificationMessage, markers: LogstashMarker) {
        markers.andAppend(
            "context",
            "Dinheiro provavelmente saiu da conta de origem e o Arbi não liberou o dinheiro na conta de destino",
        )
        internalBankService.notifyPixNotReceived(
            bankNo = ARBI_BANK_NO,
            routingNo = message.parte.codAgencia.toLong(),
            accountNumber = AccountNumber(message.parte.nroConta),
            flow = BankStatementItemFlow.CREDIT,
            counterpartName = message.contraParte.nome,
            counterpartDocument = message.contraParte.cpfCnpj,
            amount = message.valor.parseAmount(),
        )
    }

    private fun ArbiPixMovementNotificationMessage.resolveOnePixPayId(): OnePixPayId<*>? {
        return when (this.origemMovimento) {
            ArbiPixMovementOrigin.CHAVE,
            ArbiPixMovementOrigin.QR_CODE,
            ArbiPixMovementOrigin.MANUAL,
            ArbiPixMovementOrigin.DEVOLUCAO,
            ArbiPixMovementOrigin.DEVOLUCAO_ESPECIAL,
            ArbiPixMovementOrigin.INIC_PAG,
            ArbiPixMovementOrigin.INIC_PAG_QR_CODE,
            ArbiPixMovementOrigin.INIC_PAG_CHAVE,
            -> {
                toBankStatementItem()?.toUUIDTransactionId()
            }

            ArbiPixMovementOrigin.INIC_PAG_MANU -> EndToEnd(endToEnd).toOnePixPayId()
        }
    }

    private fun handleDeposit(message: ArbiPixMovementNotificationMessage, markers: LogstashMarker): Either<HttpResponse<*>, Unit> {
        val bankStatementItem = message.toBankStatementItem() ?: return HttpResponse.badRequest("nroMovimento nao pode ser nulo: $message").left()

        val onePixPayId = message.resolveOnePixPayId()
        markers.andAppend("onePixPayId", onePixPayId)

        onePixPayId?.let {
            onePixPayService.process(AccountNumber(message.parte.nroConta), routingNo = message.parte.codAgencia.toLong(), onePixPayId = it)

            with(it.source) {
                if (this is EndToEnd) {
                    sweepingAccountService.confirmSweepingCashIn(this).onRight { sweepingCashInId ->
                        markers.andAppend("sweepingCashInId", sweepingCashInId.value)
                    }.onLeft { error ->
                        markers.andAppend("confirmSweepingCashInError", error)
                    }
                }
            }
        }
        markers.andAppend("isTemporaryOperationNumber", bankStatementItem.isTemporaryOperationNumber)

        if (bankStatementItem.isTemporaryOperationNumber) {
            val queueMessage = SynchronizeBankAccountTO(
                bankNo = ARBI_BANK_NO,
                routingNo = message.parte.codAgencia.toLong(),
                fullAccountNumber = message.parte.nroConta,
            )
            messagePublisher.sendMessage(pixDepositNotificationQueue, queueMessage)
        } else {
            internalBankService.synchronizePixBankStatementItem(
                bankNo = ARBI_BANK_NO,
                routingNo = message.parte.codAgencia.toLong(),
                accountNumber = AccountNumber(message.parte.nroConta),
                bankStatementItem = bankStatementItem,
            )
        }

        messagePublisher.sendMessage(
            cashInReceivedQueue,
            CashInReceivedTO(
                bankNo = ARBI_BANK_NO,
                routingNo = message.parte.codAgencia.toLong(),
                accountNo = AccountNumber(message.parte.nroConta),
                senderBankIspb = message.contraParte.codIspb.toLong(),
            ),
        )

        if (message.tipo == ArbiPixMovementType.CREDITO_EXTERNO && message.parte.cpfCnpj == message.contraParte.cpfCnpj) {
            externalBankAccountService.saveLastUsed(externalBankAccount = message.contraParte.toExternalBankAccount())
        }

        val isOmnibusAccount = omnibusBankAccountConfiguration.isOmnibusBankAccount(
            bankNo = ARBI_BANK_NO,
            routingNo = toRoutingNo(message.parte.codAgencia),
            accountNumber = AccountNumber(message.parte.nroConta),
        )
        markers.andAppend("omnibusAccount", isOmnibusAccount)

        if (message.tipo == ArbiPixMovementType.CREDITO_EXTERNO && !isOmnibusAccount) {
            val accountId = accountService.findAccountIdByPhysicalBankAccountNo(
                bankNo = ARBI_BANK_NO,
                routingNo = message.parte.codAgencia.toLong(),
                accountNo = AccountNumber(message.parte.nroConta),
            )

            userJourneyService.trackEventAsync(accountId, UserJourneyEvent.CashInCompleted)

            checkCashInLimitExceeded(
                destinationAccount = ArbiDepositBankAccount(
                    bankNo = ARBI_BANK_NO.toString(),
                    ispb = message.parte.codIspb,
                    routingNo = message.parte.codAgencia,
                    fullAccountNo = message.parte.nroConta,
                ),
                originAccount = ArbiDepositBankAccount(
                    bankNo = null,
                    ispb = message.contraParte.codIspb,
                    routingNo = message.contraParte.codAgencia,
                    fullAccountNo = message.contraParte.nroConta,
                ),
                originDocument = message.contraParte.cpfCnpj,
                markers = markers,
                accountId = accountId,
            )
        }

        return Unit.right()
    }

    @Post("/deposit")
    fun arbiDepositCallback(@Body payload: String): HttpResponse<*> {
        val markers = append("request", payload)
        try {
            val arbiCallbackRequest = parseObjectFrom<ArbiCallbackRequest>(payload)
            val bankAccount = arbiCallbackRequest.conta.conta



            internalBankService.synchronizeBankAccount(
                bankNo = arbiCallbackRequest.conta.banco.toLong(),
                routingNo = toRoutingNo(arbiCallbackRequest.conta.agencia),
                accountNumber = AccountNumber(arbiCallbackRequest.conta.conta),
            )

            arbiCallbackRequest.toExternalBankAccount()?.let {
                externalBankAccountService.saveLastUsed(it)
            }

            val isOmnibusAccount = omnibusBankAccountConfiguration.isOmnibusBankAccount(
                bankNo = ARBI_BANK_NO,
                routingNo = toRoutingNo(arbiCallbackRequest.conta.agencia),
                accountNumber = AccountNumber(arbiCallbackRequest.conta.conta),
            )
            markers.andAppend("omnibusAccount", isOmnibusAccount)

            if (arbiCallbackRequest.inscricao != null && !isOmnibusAccount) {
                val accountId = accountService.findAccountIdByPhysicalBankAccountNo(
                    bankNo = ARBI_BANK_NO,
                    routingNo = toRoutingNo(arbiCallbackRequest.conta.agencia),
                    accountNo = AccountNumber(arbiCallbackRequest.conta.conta),
                )

                userJourneyService.trackEventAsync(accountId, UserJourneyEvent.CashInCompleted)

                checkCashInLimitExceeded(
                    destinationAccount = ArbiDepositBankAccount(
                        bankNo = ARBI_BANK_NO.toString(),
                        ispb = null,
                        routingNo = toRoutingNo(arbiCallbackRequest.conta.agencia).toString(),
                        fullAccountNo = arbiCallbackRequest.conta.conta,
                    ),
                    originAccount = ArbiDepositBankAccount(
                        bankNo = arbiCallbackRequest.bancoorigem,
                        ispb = null,
                        routingNo = if (!arbiCallbackRequest.agenciaorigem.isNullOrBlank()) arbiCallbackRequest.agenciaorigem else "",
                        fullAccountNo = if (!arbiCallbackRequest.agenciaorigem.isNullOrBlank()) arbiCallbackRequest.agenciaorigem else "",
                    ),
                    originDocument = "",
                    accountId = accountId,
                    markers = markers,
                )
            }

            LOG.info(markers, "arbiDepositCallback")
            return HttpResponse.noContent<Unit>()
        } catch (exception: JsonProcessingException) {
            LOG.error(
                markers.andAppend("ACTION", "VERIFY").andAppend("context", "Payload da requisição inválido.")
                    .andAppend("errorMessage", exception.message),
                "arbiDepositCallback",
                exception,
            )
            return HttpResponse.badRequest("Requisição inválida. Por favor, verifique o body enviado e tente novamente. ${exception.message}")
        } catch (e: Exception) {
            LOG.error(markers, "arbiDepositCallback", e)
            return when (e) {
                is PaymentMethodNotFound -> HttpResponse.badRequest(e.message)
                else -> {
                    HttpResponse.serverError(e.message)
                }
            }
        }
    }

    @Post("/boleto-refund")
    fun arbiBoletoRefund(@Body payload: String): HttpResponse<*> {
        val markers = append("request", payload)
        try {
            val arbiCallbackRequest = parseObjectFrom<ArbiBoletoRefundRequestTO>(payload)
            arbiCallbackRequest.boletodevolvido.forEach { payload ->
                var barCode = BarCode.of(payload.numbarras)
                var paidBills = billEventDBRepository.findBills(barCode).getOrElse {
                    LOG.error(
                        append("ACTION", "VERIFY")
                            .andAppend("context", "Devolução de boleto deve ser feita manualmente")
                            .andAppend("payload", payload)
                            .andAppend("barCode", barCode),
                        "arbiBoletoRefund",
                    )
                    emptyList()
                }.filter {
                    it.isPaid()
                }

                if (paidBills.isEmpty()) {
                    LOG.error(
                        append("ACTION", "VERIFY")
                            .andAppend("context", "Devolução de boleto deve ser feita manualmente")
                            .andAppend("payload", payload)
                            .andAppend("barCode", barCode),
                        "arbiBoletoRefund",
                    )
                }

                paidBills.forEach { bill ->
                    val wallet = walletService.findWallet(bill.walletId)
                    LOG.error(
                        append("ACTION", "VERIFY")
                            .andAppend("context", "Devolução de boleto deve ser feita manualmente")
                            .andAppend("payload", payload)
                            .andAppend("barCode", barCode)
                            .andAppend("billId", bill.billId)
                            .andAppend("billReceipt", bill.recipient)
                            .andAppend("walletId", bill.walletId)
                            .andAppend("walletFounderAccountId", wallet.founder.accountId.value),
                        "arbiBoletoRefund",
                    )
                }
            }

            LOG.info(markers, "arbiBoletoRefund")
            return HttpResponse.noContent<Unit>()
        } catch (exception: JsonProcessingException) {
            LOG.error(
                append("ACTION", "VERIFY").andAppend("context", "Payload da requisição inválido.")
                    .andAppend("errorMessage", exception.message),
                "arbiBoletoRefund",
                exception,
            )
            return HttpResponse.badRequest("Requisição inválida. Por favor, verifique o body enviado e tente novamente. ${exception.message}")
        } catch (e: Exception) {
            LOG.error(markers, "arbiBoletoRefund", e)
            return when (e) {
                is PaymentMethodNotFound -> HttpResponse.badRequest(e.message)
                else -> {
                    HttpResponse.serverError(e.message)
                }
            }
        }
    }

    private fun ArbiCallbackRequest.toExternalBankAccount(): ExternalBankAccount? {
        if (inscricao.isNullOrEmpty() || bancoorigem.isNullOrEmpty() || contaorigem.isNullOrEmpty() || agenciaorigem.isNullOrEmpty()) {
            return null
        }

        val documento = if (inscricao.length > CPF_SIZE) {
            inscricao.padStart(CNPJ_SIZE, '0')
        } else {
            inscricao.padStart(CPF_SIZE, '0')
        }

        return ExternalBankAccount(
            document = Document(value = documento),
            bankNo = bancoorigem.toLong(),
            bankISPB = null,
            routingNo = agenciaorigem.toLong(),
            accountNo = contaorigem.dropLast(1).toLong(),
            accountDv = contaorigem.takeLast(1),
            accountType = null,
        )
    }

    private fun checkCashInLimitExceeded(
        originAccount: ArbiDepositBankAccount,
        destinationAccount: ArbiDepositBankAccount,
        originDocument: String,
        accountId: AccountId,
        markers: LogstashMarker,
    ) {
        val routingNo = destinationAccount.routingNo.toLong()

        val account = accountService.findAccountById(accountId)

        if (account.type == UserAccountType.BASIC_ACCOUNT) {
            accountService.findPhysicalAccountPaymentMethod(
                bankNo = ARBI_BANK_NO,
                routingNo = routingNo,
                fullAccountNo = destinationAccount.fullAccountNo,
            ).map {
                val monthBegin = getLocalDate().withDayOfMonth(1).atStartOfDay(brazilTimeZone)
                val monthEnd = monthBegin.plusMonths(1).minusNanos(1)

                val totalCashInMonthly = internalBankService.getCashInByPeriod(
                    startDate = monthBegin,
                    endDate = monthEnd,
                    accountPaymentMethodId = it.id,
                )

                if (totalCashInMonthly > BASIC_ACCOUNT_CASH_IN_LIMIT * 0.8) {
                    logCashInLimitExceeded(
                        accountId = account.accountId,
                        originAccount = originAccount,
                        destinationAccount = destinationAccount,
                        originDocument = originDocument,
                        startDate = monthBegin,
                        endDate = monthEnd,
                        cashInTotal = totalCashInMonthly,
                    )
                }
            }.getOrElse {
                LOG.error(markers.andAppend("ErrorMessage", it.toString()), "arbiPixDepositCallback")
            }
        }
    }

    open fun logCashInLimitExceeded(
        accountId: AccountId,
        originAccount: ArbiDepositBankAccount,
        destinationAccount: ArbiDepositBankAccount,
        originDocument: String,
        startDate: ZonedDateTime,
        endDate: ZonedDateTime,
        cashInTotal: Long,
    ) {
        val markers = append("accountId", accountId)
            .andAppend("originAccount", originAccount)
            .andAppend("destinationAccount", destinationAccount)
            .andAppend("originDocument", originDocument)
            .andAppend("startDate", startDate)
            .andAppend("endDate", endDate)
            .andAppend("cashInTotal", cashInTotal)
            .andAppend("percentageUsed", cashInTotal / BASIC_ACCOUNT_CASH_IN_LIMIT * 100)

        LOG.warn(markers, "BasicAccountCashInLimitExceeded")
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ArbiDepositCallbackController::class.java)
    }
}

@Secured(ARBI_CALLBACK)
@Controller("/arbi")
@FridayMePoupe
open class ArbiRegisterCallbackController(
    private val registerService: RegisterService,
) {
    @Post("/registerStatus")
    fun externalRegisterStatusCallback(@Body payload: String): HttpResponse<*> {
        val externalRegisterStatusCallbackRequest = try {
            parseObjectFrom<ExternalRegisterStatusCallbackRequest>(payload)
        } catch (exception: Exception) {
            LOG.error(
                append("ACTION", "VERIFY").andAppend("context", "Payload da requisição inválido.")
                    .andAppend("errorMessage", exception.message),
                "externalRegisterStatusCallback",
                exception,
            )
            return HttpResponse.badRequest("Requisição inválida. Por favor, verifique o body enviado e tente novamente. ${exception.message}")
        }

        val markers = append("request", externalRegisterStatusCallbackRequest)
        val externalRegisterStatus = when (externalRegisterStatusCallbackRequest.status) {
            "APROVADO" -> ExternalRegisterStatus.APPROVED
            "REPROVADO" -> ExternalRegisterStatus.REJECTED
            "PROPOSTA NAO CHEGOU AO KYC. ENVIAR NOVAMENTE", "EM ANALISE", "CANCELADO" -> {
                val responseMessage = "Status intermediário ignorado."
                LOG.warn(markers.andAppend("response", responseMessage), "externalRegisterStatusCallback")
                return HttpResponse.ok(ResponseTO("2005", responseMessage))
            }

            else -> {
                val responseMessage = "Situação inválida."
                LOG.warn(markers.andAppend("response", responseMessage), "externalRegisterStatusCallback")
                return HttpResponse.badRequest(ResponseTO("4002", responseMessage))
            }
        }
        return registerService.updateAccountStatus(
            document = externalRegisterStatusCallbackRequest.document,
            status = externalRegisterStatus,
        ).map {
            LOG.info(markers.andAppend("setupAccountResult", it), "externalRegisterStatusCallback")
            when (it) {
                SetupAccountResult.AddPixKeyFailed -> HttpResponse.ok(
                    ResponseTO(
                        "2003",
                        "Cadastro aprovado e DDA em ativação, porém a chave PIX não foi criada.",
                    ),
                )

                SetupAccountResult.RegisterDDAFailed -> HttpResponse.ok(
                    ResponseTO(
                        "2002",
                        "Cadastro aprovado, porém o DDA não foi ativado e a chave PIX não foi criada.",
                    ),
                )

                SetupAccountResult.AccountApproved -> HttpResponse.ok(
                    ResponseTO(
                        "2001",
                        "Cadastro aprovado, DDA em ativação.",
                    ),
                )

                SetupAccountResult.AccountRejected -> HttpResponse.ok(ResponseTO("2004", "Cadastro rejeitado."))
            }
        }.getOrElse {
            LOG.error(append("request", externalRegisterStatusCallbackRequest), "externalRegisterStatusCallback", it)
            return when (it) {
                is AccountNotFoundException, is ItemNotFoundException -> HttpResponse.badRequest(
                    ResponseTO(
                        "4001",
                        it.message.orEmpty(),
                    ),
                )

                is AccountIsNotUnderExternalReview -> HttpResponse.status<Unit>(HttpStatus.CONFLICT)
                is RegisterNaturalPersonException -> HttpResponse.serverError(
                    ResponseTO(
                        code = "5001",
                        message = "",
                    ),
                )

                is AlreadyLockedException -> HttpResponse.serverError(
                    ResponseTO(
                        code = "5003",
                        message = it.message.orEmpty(),
                    ),
                )

                else -> HttpResponse.serverError(
                    ResponseTO(
                        code = "5002",
                        message = "Unknown error",
                    ),
                )
            }
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ArbiRegisterCallbackController::class.java)
    }
}

private fun toRoutingNo(routingNo: String) = routingNo.take(4).toLong()

fun ArbiPixPart.toExternalBankAccount() = ExternalBankAccount(
    document = Document(
        value = cpfCnpj.padStart(CPF_SIZE, '0'),
    ),
    bankNo = null,
    bankISPB = codIspb,
    routingNo = codAgencia.toLong(),
    accountNo = nroConta.dropLast(1).toLong(),
    accountDv = nroConta.takeLast(1),
    accountType = tipoConta,
)

data class ArbiPixPart(
    val codIspb: String,
    val codAgencia: String,
    val digitoAgencia: String?,
    val nroConta: String,
    val tipoConta: String,
    val cpfCnpj: String,
    val nome: String,
    val nomeInstituicao: String?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ArbiPixMovementNotificationMessage(
    val parte: ArbiPixPart,
    val contraParte: ArbiPixPart,
    val endToEnd: String,
    val tipo: ArbiPixMovementType,
    val valor: Float,
    val dataHoraMovimento: String,
    val chave: String?,
    val nroMovimento: String?,
    val origemMovimento: ArbiPixMovementOrigin,
    val naturezaMovimento: ArbiPixMovementNature,
    val indDevolucao: Boolean,
    val codOperacao: String,
    val ehAgendado: Boolean,
    val idIdempotente: String,
    val canalEntrada: String?,
    val indExterno: Boolean,
    val finalidadeDaTransacao: String?,
    val txId: String?,
    val reasons: List<ArbiPixMovementReason>?,
    val endToEndOriginal: String?,
    val campoLivre: String?,
    val infoDevolucao: String?,
    val infoErro: String?,
    val campoExtra: String?,
    val idDevolucao: String?,
    val cnpjIniciadoraPagamento: String?,
    val uuidBloqueioDevolucaoEspecial: String?,
    val uuidSolicitacaoDevolucaoEspecial: String?,
    val motivoMED: String?,
    val prestadorDoServicoDeSaque: String?,
    val modalidadeAgente: String?,
    val valorCompra: Float?,
    val valorTroco: Float?,
    val valorSaque: Float?,
)

data class ArbiPixMovementReason(
    val code: String,
    val complCode: String?,
)

enum class ArbiPixMovementType {
    CREDITO_INTERNO,
    CREDITO_EXTERNO,
    CREDITO_DEVOLUCAO,
    CREDITO_EXTERNO_NAO_FINALIZADO,
    CREDITO_INTERNO_NAO_FINALIZADO,
    CREDITO_DEVOLUCAO_NAO_FINALIZADO,
    CREDITO_EXTERNO_CANCELADO,
    CREDITO_INTERNO_CANCELADO,
    CREDITO_DEVOLUCAO_CANCELADO,

    DEBITO_INTERNO,
    DEBITO_EXTERNO,
    DEBITO_DEVOLUCAO,
    DEBITO_EXTERNO_NAO_FINALIZADO,
    DEBITO_INTERNO_NAO_FINALIZADO,
    DEBITO_DEVOLUCAO_NAO_FINALIZADO,
    DEBITO_EXTERNO_CANCELADO,
    DEBITO_INTERNO_CANCELADO,
    DEBITO_DEVOLUCAO_CANCELADO,
}

enum class ArbiPixMovementOrigin {
    CHAVE,
    QR_CODE,
    MANUAL,
    DEVOLUCAO,
    DEVOLUCAO_ESPECIAL,
    INIC_PAG,
    INIC_PAG_QR_CODE,
    INIC_PAG_MANU,
    INIC_PAG_CHAVE,
}

enum class ArbiPixMovementNature { C, D }

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "tipoNotificacao",
    defaultImpl = ArbiPixDefaultCallbackRequest::class,
)
sealed class ArbiPixCallbackRequest {
    abstract val cpfCnpj: String
}

@JsonTypeName("MOVIMENTO")
data class ArbiPixMovementCallbackRequest(
    override val cpfCnpj: String,
    val mensagem: ArbiPixMovementNotificationMessage,
) : ArbiPixCallbackRequest()

data class ArbiPixDefaultCallbackRequest(
    override val cpfCnpj: String,
    val mensagem: Any,
) : ArbiPixCallbackRequest()

enum class ArbiPixNotificationType {
    DIRETORIO,
    MOVIMENTO,
    REIVINDICACAO,
    AGENDAMENTO_DICT,
    PAGAMENTO_AGENDADO_CANCELADO,
    BLOQUEIO_DEVOLUCAO_ESPECIAL,
    DEBITO_INTERNO_AGENDADO,
    DEBITO_EXTERNO_AGENDADO,
    RELATO_INFRACAO,
    SOLICITACAO_DEVOLUCAO,
}

data class ArbiCallbackRequest(
    val valor: Number,
    val codHistorico: String,
    val descricao: String? = null,
    val bancoorigem: String? = null,
    val agenciaorigem: String? = null,
    val contaorigem: String? = null,
    val inscricao: String? = null,
    val nome: String? = null,
    val motivodevolucao: String? = null,
    val chave: String? = null,
    val conta: ArbiCallbackContaRequest,
    val numorigemstr: String? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ArbiBoletoRefundRequestTO(
    val boletodevolvido: List<ArbiBoletoRefundItemTO>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ArbiBoletoRefundItemTO(
    val numbarras: String,
    val identtit: String,
    val dtprocdev: String,
    val tpdev: String,
    val descricao: String,
    val vlrbaixa: String,
)

data class ArbiCallbackContaRequest(
    val banco: String,
    val agencia: String,
    val conta: String,
)

data class ArbiDepositBankAccount(
    val bankNo: String?,
    val ispb: String?,
    val routingNo: String,
    val fullAccountNo: String,
)

data class ExternalRegisterStatusCallbackRequest(
    @JsonProperty("inscricao") val document: String,
    @JsonProperty("nome") val name: String,
    @JsonProperty("situacao") val status: String,
    @JsonProperty("idrequisicaoarbi") val externalId: String,
    @JsonProperty("nroconta") val fullAccountNumber: String?,
)

private fun ArbiPixMovementNotificationMessage.toBankStatementItem(): DefaultBankStatementItem? {
    val dataHoraMovimentoPattern =
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss").withZone(brazilTimeZone)
    val formattedDataHoraMovimento =
        ZonedDateTime.parse(dataHoraMovimento.take(19), dataHoraMovimentoPattern)

    val flow =
        if (this.naturezaMovimento == ArbiPixMovementNature.C) BankStatementItemFlow.CREDIT else BankStatementItemFlow.DEBIT

    return nroMovimento?.let {
        DefaultBankStatementItem(
            date = formattedDataHoraMovimento.toLocalDate(),
            flow = flow,
            type = BankStatementItemType.PIX,
            description = campoLivre.orEmpty(),
            operationNumber = it,
            isTemporaryOperationNumber = isTemporaryOperationNumber(it),
            amount = valor.parseAmount(),
            counterpartName = contraParte.nome,
            counterpartDocument = contraParte.cpfCnpj,
            counterpartAccountNo = contraParte.nroConta,
            counterpartBankName = contraParte.nomeInstituicao,
            documentNumber = parte.cpfCnpj,
            ref = txId,
            lastUpdate = formattedDataHoraMovimento,
            notificatedAt = null,
        )
    }
}

internal fun Float.parseAmount(): Long {
    return (this * 100.0f).roundToLong()
}

fun DefaultBankStatementItem.toUUIDTransactionId() = ref?.let {
    val transactionId = QrCodeTransactionId.from(it)

    if (transactionId is UUIDTransactionId) {
        transactionId.toOnePixPayId()
    } else {
        null
    }
}