package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV

import ai.friday.billpayment.app.banking.Bank
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.integrations.BankDataService
import ai.friday.morning.date.dateFormat
import io.micronaut.cache.annotation.CacheConfig
import io.micronaut.cache.annotation.Cacheable
import io.micronaut.context.annotation.Primary
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.Year
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
@CacheConfig("arbi")
@Primary
@Named("arbiBankDataServiceV2")
@Requires(env = [FRIDAY_ENV, MODATTA_ENV, ME_POUPE_ENV])
open class NewArbiBankDataAdapter(
    @param:Client(
        id = "arbi",
    ) private val httpClient: RxHttpClient,
    private val configuration: ArbiConfiguration,
    private val authenticationManager: NewArbiAuthenticationManager,
    @Property(name = "recurrence.limitDate") private val limitDate: String,
) : BankDataService {

    @Cacheable
    override fun getHolidays(): List<LocalDate> {
        return fetchDomain(12)
            .mapUntilDate(LocalDate.parse(limitDate, dateFormat))
            .also { logger.info(Markers.append("list", it), "GetHolidays") }
    }

    override fun getBankCodes(): List<Bank> {
        val response = fetchDomain(2)
        logger.info(Markers.append("response", response), "ArbiGetBankCodes")
        return response.map { convertToBank(it) }
    }

    override fun getIncomeReportPDF(document: Document, year: Year): ByteArray {
        TODO("Not yet implemented")
    }

    override fun getPixParticipants(): List<FinancialInstitution> {
        val response = fetchDomain(41)
        logger.info(Markers.append("response", response), "ArbiGetPixParticipants")
        return response.map { convertToFinancialInstitution(it) }
    }

    private fun convertToBank(bankTO: DomainTO): Bank {
        return Bank(
            number = bankTO.codigodominio.toLong(),
            name = bankTO.descricaodominio.orEmpty(),
        )
    }

    private fun convertToFinancialInstitution(domainTO: DomainTO): FinancialInstitution {
        return FinancialInstitution(
            compe = domainTO.codigodominio.takeLast(3).toLongOrNull(),
            name = domainTO.descricaodominio.orEmpty(),
            ispb = domainTO.codigodominio.take(8),
        )
    }

    private fun fetchDomain(domainNumber: Int): List<DomainTO> {
        val token = authenticationManager.getToken()
        val httpRequest = HttpRequest.POST(configuration.domainV2Path, """{"dominios":{"iddominio":"$domainNumber"}}""")
            .header("client_id", configuration.clientId)
            .header("access_token", token)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.listOf(DomainTO::class.java),
            Argument.STRING,
        )
        try {
            return call.firstOrError().blockingGet()
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            logger.error(Markers.append("response", e.response.getBody(String::class.java).get()), "FetchDomain", e)
            throw ArbiAdapterException()
        }
    }

    private fun checkUnauthorized(e: HttpClientResponseException) {
        if (e.status == HttpStatus.UNAUTHORIZED) {
            logger.warn(Markers.append("error_message", "Token is expired"), "ArbiAdapter")
            authenticationManager.cleanTokens()
            throw ArbiLoginException()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(NewArbiBankDataAdapter::class.java)
    }
}