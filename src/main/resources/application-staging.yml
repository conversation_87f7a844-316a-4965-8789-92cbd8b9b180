micronaut:
  application:
    name: bill-payment-service
  server:
    cors:
      enabled: true
      configurations:
        web:
          allowed-origins-regex: '^https:\/\/use\.meupagador\.com\.br(|.)$'
  http:
    services:
      celcoin:
        url: ${configuration.staging.celcoin.url}
        read-timeout: 10s
        pool:
          max-connections: 5
          enabled: true
      settlement-service:
        url: https://liquidacao.meupagador.com.br
        read-timeout: 20s
        pool:
          max-connections: 30
          enabled: true
      arbi:
        url: https://gaph.bancoarbi.com.br
        read-timeout: 60s
        pool:
          max-connections: 30
          enabled: true
        ssl:
          enabled: true
          isInsecureTrustAllCertificates: true
      investment-manager:
        url: ${integrations.investment-manager.host}
        read-timeout: 60s
        pool:
          max-connections: 15
          enabled: true
  metrics:
    export:
      statsd:
        enabled: false
  session:
    http:
      redis:
        value-serializer: io.micronaut.jackson.serialize.JacksonObjectSerializer
  executors:
    scheduled:
      type: scheduled
      core-pool-size: 50
  security:
    enabled: true
    authentication: cookie
    token:
      jwt:
        enabled: true
        generator:
          access-token-expiration: 720000
        cookie:
          enabled: true
          login-success-target-url: https://use.meupagador.com.br/
          login-failure-target-url: https://use.meupagador.com.br/falha-ao-autenticar
          cookie-same-site: Lax
          cookie-secure: true
          cookie-domain: .meupagador.com.br
        signatures:
          jwks:
            awscognito:
              url: 'https://cognito-idp.${application.region}.amazonaws.com/${integrations.cognito.userPoolId}/.well-known/jwks.json'
            google:
              url: 'https://www.googleapis.com/oauth2/v3/certs'
            apple:
              url: 'https://appleid.apple.com/auth/keys'
          secret:
            generator:
              secret: PleaseChangeThisSecretForANewOne
              jws-algorithm: HS256
  router:
    versioning:
      enabled: true
      default-version: 2
      header:
        enabled: true
        names:
          - 'X-API-VERSION'

modules:
  manual-entry:
    enabled: true
  pfm:
    enabled: true
  chatbot-ai:
    enabled: true
  event-api:
    enabled: true
  openfinance:
    enabled: true
  push-notification:
    enabled: true
  in-app-subscription-coupon:
    enabled: true
  investment-goals:
    enabled: true

application:
  region: us-east-1
  accountNumber: ************

endpoints:
  caches:
    enabled: true
    sensitive: false
  env:
    enabled: true
    sensitive: false

concessionariaDireto:
  financialGateway: CELCOIN

integrations:
  clearsale:
    trustedCodes:
      - CRT0001
      - CRT0005
  celcoin:
    version: v5
    host: ${configuration.staging.celcoin.url}
    host-da: https://sandbox.openfinance.celcoin.dev/
    username: teste
    password: teste
    token:
      client_id: 41b44ab9a56440.teste.celcoinapi.v5
      client_secret: e9d15cde33024c1494de7480e69b7a18c09d7cd25a8446839b3be82a56a044a3
  cielo:
    host: https://apisandbox.cieloecommerce.cielo.com.br
    checkStatusHost: https://apiquerysandbox.cieloecommerce.cielo.com.br
    credentials:
      MerchantId: ce45e57f-3877-4e92-a938-4871dafb1a1f
      MerchantKey: ZZKJTBGSHQSUDAXEQGIGQAKTYGUPFMYOUUYLHOVB
  bigdatacorp:
    host: https://bigboost.bigdatacorp.com.br
    accessToken: xxx
    peoplePath: /peoplev2
    companyPath: /companies
  arbi:
    host: https://apihml-bancoarbi.sensedia.com
    authHost: https://gaph.bancoarbi.com.br
    newHost: https://gaph.bancoarbi.com.br
    newAuthHost: https://gaph.bancoarbi.com.br
    userToken: KEv7OxEmpjiy2scHdDOTMFaMfCjFV2sp
    clientId: 1553934b-b107-34c1-91e0-a7c2adf45c38 #Postman: 1847c0b1-e9db-3e45-b964-00b08ebabf6f
    clientSecret: 24498b2a-ed8f-30dd-9281-0d57b35a5e93
    contaTitular: "*********0"
    contaLiquidacao: "7140014793"
    inscricao: **************
    contaCashin: ${integrations.arbi.contaLiquidacao}
    pixCheckoutWaitTime: 100 #miliseconds
    fepweb:
      username: friday.hml
      password: Fri#2023
  cognito:
    userPoolId: ${application.region}_S0DGZEW5J
    jwtCookie:
      userPoolClientId: 3og5ib4dn9av4vdv3u8rqthvtd
  intercom:
    token: ************************************************************
    webIdVerificationSecret: uEQeg-W3ddKuYirm3LMXpOh-ePWjbSdjXCw0SdcR
    iosIdVerificationSecret: QCKelP1l1XhDIN49NT6X3ju7ECD1xwH8ORfs6FGk
    androidIdVerificationSecret: Q7sS4G6eVmbdYJO42Op-bWcrvV7xW4pRSwp6019Y
  firebase:
    measurementId: "G-BDYBP1PJEG"
  liveness:
    host: "https://liveness.meupagador.com.br"
    user: "LIVENESS_CLIENT_ID-f96298e0-a338-11ed-8b23-4f406dbefac8"
    password: "STG_ENVIRONMENT_PASSWORD"

  settlement:
    host: https://liquidacao.meupagador.com.br
    username: 77cfdc76-111e-4e8c-82e8-d3acdc013fb5
    password: $2y$19$npO3lIz0ToTa.YZ/xWx07eymYxtSw.Y8QigUc9HaT4uJsewvsLKL2
  btg:
    host: https://api-h.developer.btgpactual.com/
    clientId: fc1dddad-08bd-44ed-a368-6e0e8e22f8b1
    clientSecret: K5GDFBJZFF8VQSWS29SUB64ZDXTMFR5MM161645VWLB97UBD
    redirectUrl: "https://use.meupagador.com.br/web/saldo-adicionado-itp"
  openfinance:
    host: "https://open-finance.meupagador.com.br"
    clientid: OPEN_FINANCE_CLIENT_ID-5daed27f-daf1-49ae-93cd-67195a758064
    clientsecret: OPEN_FINANCE_CLIENT_SECRET-9c87d947-e943-4154-82d9-37286cac2816
    participants:
      - name: mock bank
        shortName: mocked
        id: 0b919e9b-bee0-4549-baa3-bb6d003575ce
  serpro:
    # Ajustar respostas na nossa collection do postman
    host: https://afa5288f-9ae6-403a-979f-16f7218b184f.mock.pstmn.io
    username: staging
    password: password-staging
  chatbotai:
    host: "https://chatbot.meupagador.com.br"
    clientid: "CHATBOT_AI_TRANSACTION_CLIENT_ID-e73bbac2-0d97-489c-8c34-7270f653f9d4"
    secret: "0j@*Pk#u-xo}%#-A+vq7.Gk8QurW2X}PN4D>6#k}TKHZe6}UGh8pT0y4=%sB"
  investment-manager:
    host: "https://investment-api.meupagador.com.br"
    clientId: "INTERNAL_STG_b366d0b6-7d09-11ef-86ad-77322bc27d20"
    clientSecret: "INTERNAL_STG_c0d303a0-7d09-11ef-9dd7-efbc07508854"

urls:
  site: https://use.meupagador.com.br
  api: https://api.meupagador.com.br

investments:
  start-at: "08:00"
  end-at: "16:30"
  maxRedemptionDays: 3655

email:
  sender:
    email: <EMAIL>
    display-name: Notificações Via1
  return-path: <EMAIL>
  bucket-unprocessed-emails: stgses-unprocessed-emails-via1
  configuration-set-name: notification_sender_configuration_set
  notification:
    email: <EMAIL>
    display-name: Notificações Via1
  receipt:
    email: <EMAIL>
    display-name: Comprovantes Via1
  newAccountReport:
    recipients: <EMAIL>
  newUpgradeAccount:
    recipients: <EMAIL>
    sensitiveRecipients: <EMAIL>
    subject: "[upgrade de conta está pendente de aprovação interna] - %s"
    message: |
      Um upgrade de conta está pendente de aprovação interna:
      Id: %s
      Nome: %s
      Email: %s
  newAccount:
    pendingInternalApprove:
      recipients: <EMAIL>
      sensitive-recipients: ${email.newAccount.pendingInternalApprove.recipients}
    pendingInternalReview:
      recipients: <EMAIL>
      sensitive-recipients: ${email.newAccount.pendingInternalReview.recipients}
    pendingActivation:
      recipients: <EMAIL>
      sensitive-recipients: ${email.newAccount.pendingActivation.recipients}

celcoin-callback:
  identity: fde714cc-61b8-493e-9086-9e36e17d1c7b
  secret: $2y$17$bkBQLvoS/NrktlZu9jilK.vofvK1Na90wpWIZonmO1g/f9zRW6dX2

revenue-cat-callback:
  identity: c5fcf4ee-966e-446f-b56a-4d9e26373c54
  secret: password

arbi-callback:
  identity: fde714cc-61b8-493e-9086-9e36e17d1c7c
  secret: $2y$17$bkBQLvoS/NrktlZu9jilK.vofvK1Na90wpWIZonmO1g/f9zRW6dX2

modatta-b2b:
  identity: 8da2b063-bff2-43d2-8377-07b00a458c31
  secret: $2y$17$bkBQLvoS/NrktlZu9jilK.vofvK1Na90wpWIZonmO1g/f9zRW6dX2

communication-centre:
  email:
    sender:
      email: <EMAIL>
      display-name: Notificações Via1
    return-path: <EMAIL>
    bucket-unprocessed-emails: stgses-unprocessed-emails-via1
    configuration-set-name: failure_rendering_notification_configuration_set
    notification:
      email: <EMAIL>
      display-name: Notificações Via1
    receipt:
      email: <EMAIL>
      display-name: Comprovantes Via1
    virus:
      bucket: stg-quarantine-emails
  forward:
    configuration-set: failure_rendering_notification_configuration_set
    sender: <EMAIL>
  integration:
    blip:
      host: https://via1-pagamentos-digitais.http.msging.net
      namespace: 1d3afeae_c48c_4c2a_8d65_02b4bbf01f83
      command:
        path: /commands
      message:
        path: /messages

emailDomain: "meupagador.com.br"

accountRegister:
  user_files:
    bucket: stg-user-documents


sqs:
  emailQueueName: stg-incoming-emails
  rollbackTransactionQueueName: bill_payment_rollback_transaction
  walletEventsQueueName: wallet-events-queue-stg
  accountEventsQueueName: account-events-queue-stg
  sqsWaitTime: 20
  dlqArn: arn:aws:sqs:${application.region}:${application.accountNumber}:bill_events_dlq

sns:
  topics:
    wallet-events:
      name: wallet-events-stg
    account-events:
      name: account-events-stg
  incomingEmailsTopicArn: ${sns.arnPrefix}stg-incoming-emails
  billEventTopicArn: ${sns.arnPrefix}bill-events-stg
  walletEventTopicArn: ${sns.arnPrefix}wallet-events-stg
  accountEventTopicArn: ${sns.arnPrefix}account-events-stg
  sms:
    maxPrice: 20.00

features:
  vehicleDebts: true
  eventBus.enabled: false
  inAppSubscription: 1
  asyncSettlement: true
  forwardEmailToManualWorkflow: false
  automaticUserRegister: true
  zeroAuthEnabled: false
  creditCardChallenge: true
  createTopic: true
  updateScheduleOnAmountLowered: true

receipt:
  bucketRegion: ${application.region}
  bucketName: stg-bill-receipts

jwtValidation:
  providers:
    google:
      audience:
        - ************-dvjuq2n41cjvau4shvgclrd10j0ponv9.apps.googleusercontent.com
        - ************-is3glgp46t71at8qfuqipi28u8mv0g5e.apps.googleusercontent.com
      issuer: "https://accounts.google.com"
    apple:
      audience:
        - br.com.meupagador.webapp
      issuer: "https://appleid.apple.com"
    msisdn:
      audience:
        - ${msisdn-authentication.jwt.audience}
      issuer: ${msisdn-authentication.jwt.issuer}

settlementFundsTransfer:
  originSettlementBankAccount:
    accountNo: *********
    accountDv: 0
  originCashinBankAccount:
    accountType: CHECKING
    accountNo: *********
    accountDv: 0
    ispb: ********

internalBankService:
  omnibusBankAccount:
    accountNo: 325513

createBillService:
  idSubscriptionBy:
    recipientDocument: **************

subscription:
  amount: 990
  dayOfMonth: 10
  description: "%s"
  recipientName: Assinatura Friday
  recipientDocument: **************
  bankAccount:
    accountType: CHECKING
    bankNo: 213
    routingNo: 1
    accountNo: *********
    accountDv: 2
    ispb: ********

redis:
  uri: redis://bill-payment-cache.aj9fwb.0001.use1.cache.amazonaws.com:6379
  cache:
    value-serializer: io.micronaut.jackson.serialize.JacksonObjectSerializer
  caches:
    balance:
      charset: 'UTF-8'
      expire-after-write: 2m
    find-pix-key-details:
      charset: 'UTF-8'
      expire-after-write: 5m
      expire-after-access: 5m



kms:
  openfinance:
    keyid: 43764d7e-efd9-48f7-8fe0-20375978f17e
  hmacKey: UJKvKAPO2aErdBmfI3Vgs7rnKJ9GmEr3P642VCqHw3odGzzKQQ1Wb2s3chozVVQDCvmdghqHmznSenYNQ+y/IQ==